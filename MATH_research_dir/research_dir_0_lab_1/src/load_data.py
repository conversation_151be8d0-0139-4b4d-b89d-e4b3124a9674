from datasets import load_dataset
import random

print("DailyDialog 데이터셋 로딩 중 (trust_remote_code=True 사용)")
dataset = load_dataset("daily_dialog", trust_remote_code=True)

# 게임 시나리오와 감정 상태 목록 정의
scenarios = ['RPG', '어드벤처', '시뮬레이션']
emotions = ['기쁨', '슬픔', '분노', '공포', '놀람', '혐오', '중립']

# 각 샘플의 대화 내용을 합치고, 임의의 감정과 시나리오 정보를 추가합니다.
def process_sample(sample):
    # 대화 리스트를 하나의 문자열로 결합
    combined_text = " ".join(sample["dialog"])
    sample["conversation"] = combined_text
    sample["emotion"] = random.choice(emotions)
    sample["scenario"] = random.choice(scenarios)
    return sample

print("전처리 중: 감정 및 시나리오 정보 추가 ...")
dataset["train"] = dataset["train"].map(process_sample)

# 첫 번째 샘플 출력으로 전처리 확인
print("전처리 완료! 첫 번째 샘플 출력:")
print(dataset["train"][0])