# If you want to have user input or be a human-in-the-loop
copilot-mode: True

# Here is the research prompt. If num-papers-to-write > 1, you can treat this as a "research direction" otherwise it can be *very* specific and can be treated as a full research idea
research-topic: "Your goal is to design reasoning and prompt engineering techniques to maximize accuracy on the entire 500 test questions of MATH500 benchmark. Your idea should be very novel."

# Here you can put your OpenAI API key--if you don't have one or OpenAI doesn't work for you, you can also instead use `deepseek-api-key`
api-key: "OPENAI-API-KEY-HERE"
# or deepseek-api-key: "DEEPSEEK-API-KEY-HERE"
# Agent Laboratory backend
llm-backend: "o3-mini"
# Literature review backend
lit-review-backend: "o3-mini"

# Base language
language: "English"

# Number of arxiv papers to lit review
num-papers-lit-review: 5
# Total number of papers to write in sequence
num-papers-to-write: 1
# Do you want to run multiple agent labs in parallel?
parallel-labs: False

# Total mle-solver steps per lab
mlesolver-max-steps: 3
# Total paper-solver steps per lab
papersolver-max-steps: 1
# The lab index for this lab (used for parallel runs)
lab-index: 1
# If you want to load an existing save
load-existing: False
# If fail, run exception?
except-if-fail: False
# Compile latex into PDFs during paper-solver
compile-latex: False

# Task notes
task-notes:
  plan-formulation:
    - 'You should come up with a plan for only ONE experiment aimed at maximizing performance on the test set of MATH using prompting techniques.'
    - 'The baseline performance of gpt-4o-mini on MATH-500 is 70.2%'
    - 'Please use gpt-4o-mini for your experiments'
    - 'You must evaluate on the entire 500 test questions of MATH'
    - 'Your plan should be a novel prompting technique'
    - 'Your evalution shound aim to get state-of-the-art performance on the MATH dataset using prompt a novel prompting idea'
    - "DO NOT PLAN FOR TOO LONG. Submit your plan soon."
  data-preparation:
    - 'Please use gpt-4o-mini for your experiments'
    - 'You must evaluate on the entire 500 test questions of MATH'
    - 'Here is a sample code you can use to load MATH\nfrom datasets import load_dataset\nMATH_test_set = load_dataset("HuggingFaceH4/MATH-500")["test"]'
  running-experiments:
    - "For all strings you instantiate you must use triple quotes (''')"
    - 'Please use gpt-4o-mini for your experiments'
    - 'Do not try to obtain baseline accuracy or any comparison points. The baseline performance of gpt-4o-mini on MATH-500 is 70.2%'
    - 'You can just use the query_gpt4omini(prompt=prompt, system=system_prompt) to prompt gpt-4o-mini. You can also access temperature by setting the temperature value query_gpt4omini(prompt=prompt, system=system_prompt, temperature=0.5) for example.'
    - 'You must evaluate on the entire 500 test questions of MATH-500'
    - "You should come up with a plan for ONE experiment aimed at maximizing performance on MATH using prompting techniques"
    - "Make sure to use is_equiv() to evaluate if two answers are equivalent."
    - 'Use the following code to inference gpt-4o-mini\nresponse = query_gpt4omini(prompt=prompt, system=system_prompt)'
    - "Your code should parallelize inference. Make sure to write parallelized code."
    - "YOU MUST MAKE YOUR CODE PARALLELIZED."
    - "Create very thoughtful figures, that would make a good research study."
    - 'You have access to only gpt-4o-mini'
    - 'Here is some sample code to evaluate on MATH:\nimport multiprocessing\nimport concurrent.futures\nfrom datasets import load_dataset\n\ndef process_example(example):\n    problem = example["problem"]\n    solution = example["solution"]\n    true_answer = remove_boxed(last_boxed_only_string(solution))\n    prompt = f"""Solve the following math problem and provide your final answer enclosed in a LaTeX \\boxed{{...}} command.\n\nProblem: {problem}\n\nFinal Answer:"""\n    response = query_gpt4omini(prompt=prompt, system="You are a skilled mathematician.")\n    llm_answer = remove_boxed(last_boxed_only_string(response))\n    correct = is_equiv(llm_answer, true_answer)\n    return llm_answer, true_answer, correct\n\ndef main():\n    math_test_set = load_dataset("HuggingFaceH4/MATH-500")["test"]\n    total, correct_count = 0, 0\n    max_workers = multiprocessing.cpu_count()\n    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:\n        futures = [executor.submit(process_example, example) for example in math_test_set]\n        for future in concurrent.futures.as_completed(futures):\n            try: llm_answer, true_answer, correct = future.result()\n            except Exception: continue\n            total += 1\n            if correct: correct_count += 1\n            print(f"Step: {total}, LLM answer: {llm_answer}, True answer: {true_answer}, Accuracy: {(correct_count / total) * 100:.2f}%")\n    print(f"Complete, final accuracy: {(correct_count / total) * 100:.2f}%")\n\nif __name__ == "__main__":\n    main()'
    - 'Generate figures with very colorful and artistic design'
  results-interpretation:
    - 'The baseline performance of gpt-4o-mini on MATH-500 is 70.2%'
  report-writing:
    - 'The baseline performance of gpt-4o-mini on MATH-500 is 70.2%'