# If you want to have user input or be a human-in-the-loop
copilot-mode: True

# Here is the research prompt. If num-papers-to-write > 1, you can treat this as a "research direction" otherwise it can be *very* specific and can be treated as a full research idea
research-topic: "LLM 기반 게임 NPC의 감정 모델링 및 적응형 대화 시스템 개발(LLM-based emotion modeling of game NPCs and development of an adaptive dialog system)"

# Here you can put your OpenAI API key--if you don't have one or OpenAI doesn't work for you, you can also instead use `deepseek-api-key`
api-key: "********************************************************************************************************************************************************************"
# or deepseek-api-key: "DEEPSEEK-API-KEY-HERE"
# Agent Laboratory backend
llm-backend: "o3-mini"
# Literature review backend
lit-review-backend: "o3-mini"

# Base language
language: "Korean"

# Number of arxiv papers to lit review
num-papers-lit-review: 5
# Total number of papers to write in sequence
num-papers-to-write: 1
# Do you want to run multiple agent labs in parallel?
parallel-labs: False

# Total mle-solver steps per lab
mlesolver-max-steps: 3
# Total paper-solver steps per lab
papersolver-max-steps: 1
# The lab index for this lab (used for parallel runs)
lab-index: 1
# If you want to load an existing save
load-existing: True
# If fail, run exception?
except-if-fail: True
# Compile latex into PDFs during paper-solver
compile-latex: False

# Task notes
task-notes:
  plan-formulation:
    - '게임 NPC의 감정 모델링 및 적응형 대화 시스템 개발을 위한 실험 계획을 수립하세요.'
    - '게임 상황에서 NPC가 플레이어와의 상호작용에 따라 감정 상태를 변화시키는 모델을 설계하세요.'
    - '감정 모델은 기쁨, 슬픔, 분노, 공포, 놀람, 혐오 등 기본 감정을 포함해야 합니다.'
    - '플레이어의 행동과 대화에 따라 NPC의 감정이 자연스럽게 변화하는 메커니즘을 설계하세요.'
    - '감정 상태에 따라 대화 스타일과 내용이 달라지는 적응형 대화 시스템을 개발하세요.'
    - '실험은 최소 3가지 이상의 게임 시나리오에서 테스트되어야 합니다.'
  data-preparation:
    - '게임 NPC 대화 데이터셋을 구축하거나 기존 데이터셋을 활용하세요.'
    - '다양한 감정 상태를 반영한 대화 예시를 수집하세요.'
    - '게임 상황에서 발생할 수 있는 다양한 시나리오를 준비하세요.'
    - '감정 상태 변화를 트리거할 수 있는 플레이어 행동 및 대화 패턴을 정의하세요.'
  running-experiments:
    - "모든 문자열은 삼중 따옴표(''')를 사용하여 인스턴스화해야 합니다."
    - '감정 모델링 알고리즘의 성능을 평가하기 위한 지표를 설정하세요.'
    - '적응형 대화 시스템의 자연스러움과 일관성을 평가하세요.'
    - '플레이어 행동에 따른 NPC 감정 변화의 적절성을 평가하세요.'
    - '다양한 게임 시나리오에서 시스템의 성능을 테스트하세요.'
    - '사용자 경험 측면에서 시스템의 효과를 평가하세요.'
    - '코드는 병렬 처리를 통해 효율적으로 실행되어야 합니다.'
    - '연구 결과를 시각화하는 창의적이고 명확한 그래프와 차트를 생성하세요.'
  results-interpretation:
    - '감정 모델링의 정확성과 자연스러움에 대한 결과를 분석하세요.'
    - '적응형 대화 시스템의 효과성을 평가하세요.'
    - '시스템의 강점과 약점을 식별하고 개선 방향을 제시하세요.'
    - '게임 경험 향상에 대한 시스템의 기여도를 평가하세요.'
  report-writing:
    - '연구 방법론, 실험 설계, 결과 및 해석을 포함한 종합적인 보고서를 작성하세요.'
    - '게임 NPC 감정 모델링 및 적응형 대화 시스템의 실제 게임 적용 가능성을 논의하세요.'
    - '향후 연구 방향 및 개선 사항을 제안하세요.'