# Adaptive NPC Emotion Modeling and Dialogue Systems

This repository contains the code, experimental setup, and supporting materials for our research on integrating Large Language Models (LLMs), Graph Neural Networks (GNNs), and Reinforcement Learning (RL) to build adaptive and emotion-aware NPC dialogue systems in dynamic gaming environments.

---

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Architecture and Methodology](#architecture-and-methodology)
- [Installation](#installation)
- [Usage](#usage)
- [Experimental Setup](#experimental-setup)
- [Results](#results)
- [Discussion and Future Work](#discussion-and-future-work)
- [References](#references)
- [License](#license)

---

## Overview

This project presents a novel integrated framework designed to improve NPC dialogue generation and emotional state adaptation in real-time interactive game environments. The core idea is to model an NPC's emotion as a vector (𝒆 ∈ ℝⁿ) that is dynamically updated based on player actions and in-game events. Our approach utilizes:

- **Large Language Models (LLMs):** For generating contextually appropriate and natural dialogue.
- **Graph Neural Networks (GNNs):** To capture the relational and contextual structures between players, NPCs, and game events.
- **Reinforcement Learning (RL):** To adapt the NPC's emotional state using a reward signal that balances dialogue quality and emotion consistency.

This repository includes the complete implementation details, experiment code, simulation setup, and evaluation metrics to demonstrate significant improvements over conventional text-based approaches.

---

## Features

- **Dynamic Emotion Modeling:** Uses the emotion update rule:
  
  𝒆<sub>new</sub> = 𝒆 + Δ𝒆

  where Δ𝒆 is computed as:

  Δ𝒆 = f<sub>GNN</sub>(player, event) + f<sub>RL</sub>(r)
  
- **Hybrid Model Architecture:** Integrates LLM for natural language generation, GNN for relational context extraction, and RL for continuous improvement.
- **Real-Time Simulation Environment:** Demonstrated via Unity-based simulation and publicly available dialogue datasets (e.g., DailyDialog).
- **Dual-Objective Loss Function:** Optimizes both dialogue appropriateness and emotion consistency:
  
  L = λ<sub>dialog</sub> · L<sub>dialog</sub> + λ<sub>emotion</sub> · ‖𝒆<sub>new</sub> – 𝒕‖₂²
- **Extensive Evaluation Metrics:** Includes response appropriateness, emotion consistency (L2 distance), RL reward dynamics, and convergence behavior.

---

## Architecture and Methodology

Our model represents the NPC's emotional state as an n-dimensional vector. The update mechanism is given by:

  𝒆<sub>new</sub> = 𝒆 + Δ𝒆

with:
  
  Δ𝒆 = f<sub>GNN</sub>(p, q) + f<sub>RL</sub>(r)

- **Player Actions (p) and Game Events (q):** Mapped to a graph where nodes represent entities and edges represent interactions.
- **Reinforcement Learning Reward (r):** Calculated as:

  r = max(0, min(1, L/200 + η)), η ∼ U(0, 0.2)

- **Multi-Objective Optimization:** The loss function balances dialogue generation quality and desired emotional state consistency using carefully tuned hyperparameters (λ<sub>dialog</sub> and λ<sub>emotion</sub>).

For more detailed mathematical formulation and experimental details, please refer to the attached PDF documentation and research paper.

---

## Installation

### Prerequisites

- [Python 3.8+](https://www.python.org/downloads/)
- [PyTorch](https://pytorch.org/get-started/locally/)
- [NetworkX](https://networkx.org/)
- [Unity (for simulation environment)](https://unity.com/)

### Clone the Repository

```bash
git clone https://github.com/yourusername/adaptive-npc-emotion-modeling.git
cd adaptive-npc-emotion-modeling
```

### Install Python Dependencies

```bash
pip install -r requirements.txt
```

*Note: The `requirements.txt` file includes all necessary libraries such as PyTorch, NetworkX, and other helper packages.*

---

## Usage

### Running the Simulation

1. **Unity Environment:**
   - Open the provided Unity project folder to run the simulation scenarios (RPG, Adventure, Simulation).
   
2. **Training the Model:**
   - Execute the training script to initiate the multi-objective learning process:
   
   ```bash
   python train.py --config config.yaml
   ```
   
   - This script will process the dialogue dataset (e.g., DailyDialog), set up the GNN graph from interaction logs, and train the fusion model using reinforcement learning techniques.

3. **Evaluation:**
   - After training, you can run evaluation scripts to measure response appropriateness and emotion consistency:
   
   ```bash
   python evaluate.py --model saved_model.pth
   ```

### Configuration

Customize the training and simulation parameters via the `config.yaml` file. Key parameters include:
- Learning rates
- RL reward function parameters
- Hyperparameters (λ<sub>dialog</sub>, λ<sub>emotion</sub>)
- Seed value for reproducibility (set to 42)

---

## Experimental Setup

Our experiments were conducted on both simulated game environments (using Unity) and real-world dialogue datasets (DailyDialog). Key components of the experiment include:
- **Simulation Scenarios:** RPG, Adventure, and Simulation setups to emulate varying interaction complexities.
- **Preprocessing Pipeline:** Converts multimodal inputs (text, player behavior, and in-game events) into a graph structure and standardized reward signals.
- **Ablation Studies:** Verify the importance of GNN and RL modules by comparing to text-based Transformer baseline.

---

## Results

The proposed model has demonstrated significant improvements:
- **Response Appropriateness:** Improved from 0.657 (baseline) to 0.826.
- **Emotion Consistency:** Increased from 0.631 (baseline) to 0.775.
- **RL Convergence:** RL loss decreases from 0.3498 to 0.1839, with an average reward of ~0.960.
- **Quantitative Emotion Adaptation Score:** Approximately 64.74% based on L2 distance measures.

These results validate the effectiveness of the fusion approach in generating contextually relevant dialogue and dynamically adjusting NPC emotions.

---

## Discussion and Future Work

- **Key Contributions:**
  - Integration of LLM, GNN, and RL to address both natural language generation and adaptive emotional modeling.
  - A robust multi-modal system that overcomes limitations of static and rule-based NPC dialogue systems.
  
- **Future Directions:**
  - Further tuning of the reward function and emotional update parameters.
  - Integration of additional modalities such as voice and facial expressions.
  - Scaling to multi-agent environments and more complex game scenarios.

Please see the paper and accompanying documentation for in-depth discussions and future perspectives.

---

## References

- Extreme AI (arXiv:1609.04879v1)
- Dialogue Shaping (arXiv:2307.15833v1)
- DVM (arXiv:2501.06695v1)
- Additional related work listed in the research paper.

---

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

---

For any inquiries or further information, please contact [Author Name or Contact Email].

Happy coding and enjoy exploring adaptive NPC interactions!